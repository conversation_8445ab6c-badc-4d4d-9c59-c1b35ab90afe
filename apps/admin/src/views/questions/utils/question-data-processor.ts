/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */
import { nanoid } from '@sa/utils'

// 定义流式数据的接口
export interface StreamQuestionData {
  Question: {
    QuestionType: string
    QuestionTypeId: string
    Title: string
    Options?: Array<{
      Option: string
      Content: string
    }>
    Answer: string
    Analysis: string
    KnowledgePoints: string[] | null
  }
}

// 定义组件需要的数据格式
export interface ProcessedQuestionData {
  id: string
  typeText: string
  typeId: string
  title: string
  componentsName: string
  options?: Array<{
    label: string
    value: string
  }>
  correctAnswer: string
  analysis: {
    title: string
    content: string[]
  }
  knowledgePoints: string[]
}

// 题型ID映射
export const QUESTION_TYPE_MAP = {
  1: 'multiple-choice', // 多选题
  2: 'single-choice', // 单选题
  3: 'true-false', // 判断题
  4: 'fill-blank', // 填空题
  5: 'short-answer', // 简答题
  6: 'essay', // 论述题
} as const

/**
 * 处理单选题数据
 */
function processSingleChoiceData(data: StreamQuestionData): ProcessedQuestionData {





  const { 
    QuestionType: typeText, 
    QuestionTypeId: typeId, 
    Title: title, 
    Options = [], 
    Answer: correctAnswer, 
    Analysis, 
    KnowledgePoints = [] 
  } = data.Question

  return {
    id: nanoid(),





    typeText,
    typeId,
    title,
    options: Options.map(option => ({
      label: option.Content,
      value: option.Option,
    })),
    correctAnswer,
    analysis: {
      title: '正确答案：',
      content: [Analysis],
    },

    knowledgePoints: KnowledgePoints,
  }
}

/**
 * 处理多选题数据
 */
function processMultipleChoiceData(data: StreamQuestionData): ProcessedQuestionData {
  const options = data.Question.Options?.map(option => ({
    label: option.Content,
    value: option.Option,
  })) || []

  return {
    id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: data.Question.QuestionType,
    typeId: data.Question.QuestionTypeId,
    title: data.Question.Title,
    content: data.Question.Title,
    options,
    correctAnswer: data.Question.Answer,
    analysis: {
      title: '答案解析：',
      content: [data.Question.Analysis],
    },
    knowledgePoints: data.Question.KnowledgePoints || [],
    progress: data.Progress,
  }
}

/**
 * 处理判断题数据
 */
function processTrueFalseData(data: StreamQuestionData): ProcessedQuestionData {
  const options = [
    { label: '正确', value: 'T' },
    { label: '错误', value: 'F' },
  ]

  return {
    id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: data.Question.QuestionType,
    typeId: data.Question.QuestionTypeId,
    title: data.Question.Title,
    content: data.Question.Title,
    options,
    correctAnswer: data.Question.Answer,
    analysis: {
      title: '答案解析：',
      content: [data.Question.Analysis],
    },
    knowledgePoints: data.Question.KnowledgePoints || [],
    progress: data.Progress,
  }
}

/**
 * 处理填空题数据
 */
function processFillBlankData(data: StreamQuestionData): ProcessedQuestionData {
  return {
    id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: data.Question.QuestionType,
    typeId: data.Question.QuestionTypeId,
    title: data.Question.Title,
    content: data.Question.Title,
    correctAnswer: data.Question.Answer,
    analysis: {
      title: '答案解析：',
      content: [data.Question.Analysis],
    },
    knowledgePoints: data.Question.KnowledgePoints || [],
    progress: data.Progress,
  }
}

/**
 * 处理简答题数据
 */
function processShortAnswerData(data: StreamQuestionData): ProcessedQuestionData {
  return {
    id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: data.Question.QuestionType,
    typeId: data.Question.QuestionTypeId,
    title: data.Question.Title,
    content: data.Question.Title,
    correctAnswer: data.Question.Answer,
    analysis: {
      title: '答案解析：',
      content: [data.Question.Analysis],
    },
    knowledgePoints: data.Question.KnowledgePoints || [],
    progress: data.Progress,
  }
}

/**
 * 主要的数据处理函数
 * 根据题型ID处理不同类型的题目数据
 */
export function processQuestionData(data: StreamQuestionData): ProcessedQuestionData | null {
  if (!data.Success || data.MessageType !== 'question' || !data.Question) {
    return null
  }

  const questionTypeId = data.Question.QuestionTypeId

  switch (questionTypeId) {
    case '1': // 多选题
      return processMultipleChoiceData(data)
    case '2': // 单选题
      return processSingleChoiceData(data)
    case '3': // 判断题
      return processTrueFalseData(data)
    case '4': // 填空题
      return processFillBlankData(data)
    case '5': // 简答题
      return processShortAnswerData(data)
    case '6': // 论述题
      return processShortAnswerData(data) // 论述题和简答题处理方式相同
    default:
      console.warn(`未知的题型ID: ${questionTypeId}`)
      return processSingleChoiceData(data) // 默认按单选题处理
  }
}

/**
 * 获取题型对应的组件名称
 */
export function getQuestionComponentName(typeId: string): string {
  const componentMap: Record<string, string> = {
    1: 'MultipleChoice',
    2: 'SingleChoice',
    3: 'TrueFalse',
    4: 'FillBlank',
    5: 'ShortAnswer',
    6: 'Essay',
  }

  return componentMap[typeId] || 'SingleChoice'
}

/**
 * 验证题目数据是否完整
 */
export function validateQuestionData(data: ProcessedQuestionData): boolean {
  return !!(
    data.id
    && data.type
    && data.typeId
    && data.title
    && data.correctAnswer
    && data.analysis
  )
}
